
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Note, Attachment, Theme, Language } from '../types';
import Icon from './Icon';
import Modal from './Modal';
import * as geminiService from '../services/geminiService';
import { t } from '../i18n';
import TurndownService from 'turndown';

interface NoteEditorProps {
  note: Note;
  allNotes: Note[];
  onSave: (note: Note) => void;
  onDelete: (id: string) => void;
  onBack: () => void;
  onNavigateToNote: (id: string) => void;
  addNote: (note: Note) => void;
  theme: Theme;
  language: Language;
  openAssistant: (context?: { currentNote: Note; subNotes: Note[] } | null) => void;
}

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  theme: Theme;
  onBlur?: () => void;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ value, onChange, theme, onBlur }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<any>(null);
  
  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    onChange(e.currentTarget.innerHTML);
  };
  
  const execCmd = (cmd: string, value?: string) => {
    document.execCommand(cmd, false, value);
    editorRef.current?.focus();
    onChange(editorRef.current?.innerHTML || '');
  };

  """  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleVoiceInput = () => {
    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('Your browser does not support Speech Recognition.');
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = language === 'zh' ? 'zh-CN' : 'en-US';
    recognitionRef.current = recognition;

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.onresult = (event) => {
      let interimTranscript = '';
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
      // For a smoother experience, we can update the editor with the final transcript
      if (finalTranscript) {
        editorRef.current?.focus();
        document.execCommand('insertText', false, finalTranscript);
      }
    };
    recognition.start();
  };

  const handleAiPolish = async () => {
    const selection = window.getSelection();
    const selectedText = selection?.toString().trim();

    if (!selectedText) {
      alert('Please select text to polish.');
      return;
    }

    try {
      const polishedText = await geminiService.optimizeText(selectedText, language);
      if (polishedText) {
        document.execCommand('insertText', false, polishedText);
      }
    } catch (error) {
      console.error('AI polish failed:', error);
      alert('Failed to polish text with AI.');
    }
  };

  return (
    <div>
      <div className={`flex flex-wrap items-center gap-2 p-2 border rounded-t-md ${theme.secondaryBg}`}>
        <button onClick={() => execCmd('bold')} title="Bold" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-bold" /></button>
        <button onClick={() => execCmd('italic')} title="Italic" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-italic" /></button>
        <button onClick={() => execCmd('underline')} title="Underline" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-underline" /></button>
        <button onClick={() => execCmd('strikeThrough')} title="Strikethrough" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-strikethrough" /></button>
        <div className="h-5 border-l border-white/20 mx-1"></div>
        <button onClick={() => execCmd('formatBlock', '<h1>')} title="Heading 1" className="p-2 hover:bg-white/20 rounded font-bold">H1</button>
        <button onClick={() => execCmd('formatBlock', '<h2>')} title="Heading 2" className="p-2 hover:bg-white/20 rounded font-bold">H2</button>
        <button onClick={() => execCmd('formatBlock', '<blockquote>')} title="Blockquote" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-quote-left" /></button>
        <div className="h-5 border-l border-white/20 mx-1"></div>
        <button onClick={() => execCmd('insertUnorderedList')} title="Unordered List" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-list-ul" /></button>
        <button onClick={() => execCmd('insertOrderedList')} title="Ordered List" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-list-ol" /></button>
        <div className="h-5 border-l border-white/20 mx-1"></div>
        <button onClick={() => execCmd('justifyLeft')} title="Align Left" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-align-left" /></button>
        <button onClick={() => execCmd('justifyCenter')} title="Align Center" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-align-center" /></button>
        <button onClick={() => execCmd('justifyRight')} title="Align Right" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-align-right" /></button>
        <input type="color" onChange={e => execCmd('foreColor', e.target.value)} title="Text Color" className="w-8 h-8 bg-transparent border-none cursor-pointer" />
        <div className="h-5 border-l border-white/20 mx-1"></div>
        <button onClick={handleVoiceInput} title="Voice Input" className={`p-2 hover:bg-white/20 rounded ${isListening ? 'bg-red-500' : ''}`}><Icon icon="fa-microphone" /></button>
        <button onClick={handleAiPolish} title="AI Polish" className="p-2 hover:bg-white/20 rounded"><Icon icon="fa-magic" /></button>
      </div>
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onBlur={onBlur}
        className={`w-full min-h-[200px] p-4 border-x border-b rounded-b-md focus:outline-none focus:ring-2 focus:ring-pink-500 ${theme.cardBg}`}
      />
    </div>
  );
};""

type DrawingTool = 'pen' | 'eraser' | 'rectangle' | 'circle' | 'line' | 'pan';

const DrawingCanvas: React.FC<{ dataUrl: string | null, onSave: (dataUrl: string) => void, theme: Theme, language: Language }> = ({ dataUrl, onSave, theme, language }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const offscreenCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState<DrawingTool>('pen');
  const [color, setColor] = useState(theme.className === 'dark' || theme.className === 'radiant' ? '#FFFFFF' : '#000000');
  const [lineWidth, setLineWidth] = useState(5);
  const [zoom, setZoom] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const panStart = useRef({ x: 0, y: 0 });
  const startPos = useRef<{x: number, y: number} | null>(null);

  const renderVisibleCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const offscreenCanvas = offscreenCanvasRef.current;
    if (!canvas || !offscreenCanvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.save();
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.translate(panOffset.x, panOffset.y);
    ctx.scale(zoom, zoom);
    ctx.drawImage(offscreenCanvas, 0, 0);
    ctx.restore();
  }, [panOffset, zoom]);
  
  useEffect(() => {
    renderVisibleCanvas();
  }, [renderVisibleCanvas]);

  const getTransformedCoords = useCallback((e: MouseEvent | TouchEvent): { x: number; y: number } | null => {
    const canvas = canvasRef.current;
    if (!canvas) return null;
    const rect = canvas.getBoundingClientRect();
    
    let clientX, clientY;
    if ('touches' in e && e.touches.length > 0) {
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
    } else if ('changedTouches' in e && e.changedTouches.length > 0) { // For touchend
        clientX = e.changedTouches[0].clientX;
        clientY = e.changedTouches[0].clientY;
    } else if ('clientX' in e) { // For mouse events
        clientX = e.clientX;
        clientY = e.clientY;
    } else {
        return null;
    }
    
    return {
      x: (clientX - rect.left - panOffset.x) / zoom,
      y: (clientY - rect.top - panOffset.y) / zoom,
    };
  }, [panOffset, zoom]);

  const startDrawing = useCallback((e: MouseEvent | TouchEvent) => {
    e.preventDefault();
    const coords = getTransformedCoords(e);
    if (!coords) return;
    
    setIsDrawing(true);

    if (tool === 'pan') {
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      panStart.current = { x: clientX - panOffset.x, y: clientY - panOffset.y };
      return;
    }

    startPos.current = coords;
    
    if (tool === 'pen' || tool === 'eraser') {
        const ctx = offscreenCanvasRef.current?.getContext('2d');
        if (!ctx) return;
        ctx.beginPath();
        ctx.moveTo(coords.x, coords.y);
    }
  }, [getTransformedCoords, tool, panOffset]);

  const draw = useCallback((e: MouseEvent | TouchEvent) => {
    if (!isDrawing) return;
    e.preventDefault();

    if (tool === 'pan') {
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      setPanOffset({
        x: clientX - panStart.current.x,
        y: clientY - panStart.current.y,
      });
      return;
    }
    
    const coords = getTransformedCoords(e);
    if (!coords || !startPos.current) return;
    
    const offscreenCtx = offscreenCanvasRef.current?.getContext('2d');
    if (!offscreenCtx) return;

    if (tool === 'pen' || tool === 'eraser') {
      offscreenCtx.globalCompositeOperation = tool === 'eraser' ? 'destination-out' : 'source-over';
      offscreenCtx.strokeStyle = color;
      offscreenCtx.lineWidth = lineWidth;
      offscreenCtx.lineCap = 'round';
      offscreenCtx.lineJoin = 'round';
      
      offscreenCtx.lineTo(coords.x, coords.y);
      offscreenCtx.stroke();
      renderVisibleCanvas();
    } else if (['line', 'rectangle', 'circle'].includes(tool)) {
        renderVisibleCanvas(); // Redraw base canvas to clear old preview
        
        const canvas = canvasRef.current;
        const ctx = canvas?.getContext('2d');
        if (!ctx) return;
        
        // Draw shape preview on top
        ctx.save();
        ctx.translate(panOffset.x, panOffset.y);
        ctx.scale(zoom, zoom);
        
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.setLineDash([6 / zoom, 3 / zoom]);
        
        const startX = startPos.current.x;
        const startY = startPos.current.y;
        const endX = coords.x;
        const endY = coords.y;

        ctx.beginPath();
        if (tool === 'line') {
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
        } else if (tool === 'rectangle') {
            ctx.rect(startX, startY, endX - startX, endY - startY);
        } else if (tool === 'circle') {
            const radiusX = Math.abs(endX - startX) / 2;
            const radiusY = Math.abs(endY - startY) / 2;
            const centerX = startX + (endX - startX) / 2;
            const centerY = startY + (endY - startY) / 2;
            ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
        }
        ctx.stroke();
        
        ctx.restore();
    }
  }, [isDrawing, tool, getTransformedCoords, color, lineWidth, zoom, panOffset, renderVisibleCanvas]);

  const stopDrawing = useCallback((e: MouseEvent | TouchEvent) => {
    if (!isDrawing) return;
    setIsDrawing(false);

    const offscreenCtx = offscreenCanvasRef.current?.getContext('2d');
    if (!offscreenCtx || !offscreenCanvasRef.current) return;
    
    if (['line', 'rectangle', 'circle'].includes(tool) && startPos.current) {
        const finalCoords = getTransformedCoords(e);
        if (finalCoords) {
            offscreenCtx.strokeStyle = color;
            offscreenCtx.lineWidth = lineWidth;
            offscreenCtx.lineCap = 'round';
            offscreenCtx.lineJoin = 'round';
            offscreenCtx.globalCompositeOperation = 'source-over';
            
            const startX = startPos.current.x;
            const startY = startPos.current.y;
            const endX = finalCoords.x;
            const endY = finalCoords.y;

            offscreenCtx.beginPath();
            if (tool === 'line') {
                offscreenCtx.moveTo(startX, startY);
                offscreenCtx.lineTo(endX, endY);
            } else if (tool === 'rectangle') {
                offscreenCtx.rect(startX, startY, endX - startX, endY - startY);
            } else if (tool === 'circle') {
                const radiusX = Math.abs(endX - startX) / 2;
                const radiusY = Math.abs(endY - startY) / 2;
                const centerX = startX + (endX - startX) / 2;
                const centerY = startY + (endY - startY) / 2;
                offscreenCtx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
            }
            offscreenCtx.stroke();
        }
    } else if (tool === 'pen' || tool === 'eraser') {
         offscreenCtx.closePath();
    }
    
    offscreenCtx.globalCompositeOperation = 'source-over';
    
    onSave(offscreenCanvasRef.current.toDataURL());
    startPos.current = null;
    renderVisibleCanvas();

  }, [isDrawing, onSave, tool, getTransformedCoords, color, lineWidth, renderVisibleCanvas]);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const preventDefault = (e: Event) => e.preventDefault();
    canvas.addEventListener('touchstart', preventDefault, { passive: false });
    canvas.addEventListener('touchmove', preventDefault, { passive: false });

    const handleMouseDown = (e: MouseEvent) => startDrawing(e);
    const handleMouseMove = (e: MouseEvent) => draw(e);
    const handleMouseUp = (e: MouseEvent) => stopDrawing(e);
    
    const handleTouchStart = (e: TouchEvent) => startDrawing(e);
    const handleTouchMove = (e: TouchEvent) => draw(e);
    const handleTouchEnd = (e: TouchEvent) => stopDrawing(e);
    
    canvas.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    
    canvas.addEventListener('touchstart', handleTouchStart);
    window.addEventListener('touchmove', handleTouchMove);
    window.addEventListener('touchend', handleTouchEnd);

    return () => {
      canvas.removeEventListener('touchstart', preventDefault);
      canvas.removeEventListener('touchmove', preventDefault);

      canvas.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      
      canvas.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleTouchEnd);
    };
  }, [startDrawing, draw, stopDrawing]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const container = canvas.parentElement;
    if (!container) return;
    
    const { width, height } = container.getBoundingClientRect();
    canvas.width = width;
    canvas.height = height;

    if (!offscreenCanvasRef.current) {
      offscreenCanvasRef.current = document.createElement('canvas');
    }
    const offscreenCanvas = offscreenCanvasRef.current;
    offscreenCanvas.width = width;
    offscreenCanvas.height = height;

    const ctx = offscreenCanvas.getContext('2d');
    if (!ctx) return;

    if (dataUrl) {
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);
        ctx.drawImage(img, 0, 0);
        renderVisibleCanvas();
      };
      img.src = dataUrl;
    } else {
      ctx.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);
      renderVisibleCanvas();
    }
  }, [dataUrl, renderVisibleCanvas]);
  
  useEffect(() => {
     setColor(theme.className === 'dark' || theme.className === 'radiant' ? '#FFFFFF' : '#000000')
  }, [theme]);

  const clearCanvas = () => {
    if(!offscreenCanvasRef.current) return;
    const ctx = offscreenCanvasRef.current.getContext('2d');
    if(!ctx) return;
    ctx.clearRect(0, 0, offscreenCanvasRef.current.width, offscreenCanvasRef.current.height);
    renderVisibleCanvas();
    onSave('');
  }
  
  const resetView = () => {
    setZoom(1);
    setPanOffset({ x: 0, y: 0 });
  };

  const toolIsActive = (t: DrawingTool) => tool === t ? theme.accent : 'transparent';
  const toolIcons = { pen: 'fa-pen', eraser: 'fa-eraser', pan: 'fa-hand-paper', line: 'fa-slash', rectangle: 'fa-square', circle: 'fa-circle' };

  return (
    <div>
        <div className={`flex flex-wrap items-center gap-2 p-2 rounded-md mb-2 ${theme.secondaryBg}`}>
            {(Object.keys(toolIcons) as DrawingTool[]).map(t_key => (
                <button key={t_key} onClick={() => setTool(t_key)} title={t(t_key, language)} className={`p-2 rounded-md hover:bg-white/20`} style={{backgroundColor: toolIsActive(t_key)}}>
                    <Icon icon={toolIcons[t_key as keyof typeof toolIcons]}/>
                </button>
            ))}
            <input type="color" value={color} onChange={(e) => setColor(e.target.value)} className="w-8 h-8 bg-transparent border-none cursor-pointer" />
            <div className="flex items-center gap-2">
                <Icon icon="fa-ruler-horizontal"/>
                <input type="range" min="1" max="50" value={lineWidth} onChange={(e) => setLineWidth(Number(e.target.value))} className="w-24 cursor-pointer" />
                <span className='text-xs w-6'>{lineWidth}</span>
            </div>
            <div className="flex items-center gap-1 ml-4">
                <button onClick={() => setZoom(z => z / 1.2)} className="p-2 rounded-md hover:bg-white/20"><Icon icon="fa-minus"/></button>
                <button onClick={resetView} className="p-2 rounded-md hover:bg-white/20 text-xs w-16">{(zoom * 100).toFixed(0)}%</button>
                <button onClick={() => setZoom(z => z * 1.2)} className="p-2 rounded-md hover:bg-white/20"><Icon icon="fa-plus"/></button>
            </div>
            <button onClick={clearCanvas} className="p-2 rounded-md hover:bg-white/20 ml-auto"><Icon icon="fa-trash" className="mr-1"/> {t('clear', language)}</button>
        </div>
        <div className="h-[400px] bg-white/10 rounded-lg overflow-hidden cursor-crosshair">
          <canvas ref={canvasRef} />
        </div>
    </div>
  );
};

const NoteEditor: React.FC<NoteEditorProps> = ({ note, allNotes, onSave, onDelete, onBack, onNavigateToNote, addNote, theme, language, openAssistant }) => {
  const [title, setTitle] = useState(note.title);
  const [content, setContent] = useState(note.content);
  const [drawingDataUrl, setDrawingDataUrl] = useState(note.drawingDataUrl);
  const [attachments, setAttachments] = useState<Attachment[]>(note.attachments);
  const [activeTab, setActiveTab] = useState<'write' | 'draw'>('write');
  const [isAiModalOpen, setAiModalOpen] = useState(false);
  const [aiResult, setAiResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const subNotes = allNotes.filter(n => n.parentId === note.id);

  const getBreadcrumbs = useCallback(() => {
    const crumbs = [];
    let current: Note | undefined = note;
    while (current) {
        crumbs.unshift(current);
        current = allNotes.find(n => n.id === current?.parentId);
    }
    return crumbs;
  }, [note, allNotes]);

  const breadcrumbs = getBreadcrumbs();

  const handleSave = useCallback(() => {
    onSave({ ...note, title, content, drawingDataUrl, attachments });
  }, [onSave, note, title, content, drawingDataUrl, attachments]);

  useEffect(() => {
    // Sync state when the note prop changes (e.g., navigating to a new note)
    setTitle(note.title);
    setContent(note.content);
    setDrawingDataUrl(note.drawingDataUrl);
    setAttachments(note.attachments);
    setActiveTab('write'); // Reset to write tab when note changes
  }, [note.id]);

  const handleBack = () => {
    handleSave();
    onBack();
  };
  
  const handleDelete = () => {
    if (window.confirm(t('deleteConfirm', language))) {
      onDelete(note.id);
      onBack();
    }
  };

  const handleCreateSubNote = () => {
    const newNote: Note = {
      id: `note_${Date.now()}`,
      title: 'Untitled Sub-note',
      content: '<p>Details about the parent note...</p>',
      drawingDataUrl: null,
      attachments: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      motto: null,
      parentId: note.id,
    };
    addNote(newNote);
    onNavigateToNote(newNote.id);
  };

  const handleAttachmentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      Array.from(e.target.files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (event) => {
          const dataUrl = event.target?.result as string;
          const newAttachment: Attachment = {
            name: file.name,
            type: file.type,
            size: file.size,
            dataUrl,
          };
          setAttachments(prev => [...prev, newAttachment]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleAiAction = async (action: 'summarize' | 'optimize' | 'plan') => {
    setIsLoading(true);
    setAiResult('');
    setAiModalOpen(true);
    let result = '';
    const plainTextContent = new DOMParser().parseFromString(content, 'text/html').body.textContent || '';

    switch (action) {
        case 'summarize':
            result = await geminiService.summarizeNote(plainTextContent, language);
            break;
        case 'optimize':
            result = await geminiService.optimizeText(plainTextContent, language);
            break;
        case 'plan':
            result = await geminiService.createItinerary(plainTextContent, language);
            break;
    }
    setAiResult(result);
    setIsLoading(false);
  }

  const applyAiResult = () => {
    setContent(prev => `${prev}<hr><p><b>AI Result:</b></p><p>${aiResult.replace(/\n/g, '<br>')}</p>`);
    setAiModalOpen(false);
  }

  const handleExportAsMarkdown = () => {
    const turndown = new TurndownService();
    const markdown = turndown.turndown(content);
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title || 'note'}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  const handleOpenAssistant = () => {
    openAssistant({ currentNote: note, subNotes });
  };
  
  const TabButton = ({ tab, icon, label }: { tab: 'write' | 'draw', icon: string, label: string }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${activeTab === tab ? theme.accent + ' text-white' : theme.secondaryBg}`}
    >
      <Icon icon={icon} /> {label}
    </button>
  );

  return (
    <div className="p-4 sm:p-8 max-w-4xl mx-auto">
        <header className="flex justify-between items-center mb-6">
            <div className="flex-grow min-w-0">
                <div className="flex items-center text-sm opacity-80 mb-2">
                    {breadcrumbs.map((crumb, index) => (
                        <React.Fragment key={crumb.id}>
                            {index > 0 && <span className="mx-2">/</span>}
                            <button 
                                onClick={() => index === 0 ? onBack() : onNavigateToNote(crumb.id)}
                                className="hover:underline truncate"
                                disabled={crumb.id === note.id}
                            >
                                {index === 0 ? t('dashboard', language) : (crumb.title || 'Untitled')}
                            </button>
                        </React.Fragment>
                    ))}
                </div>
                <input
                  type="text"
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  onBlur={handleSave}
                  placeholder={t('noteTitlePlaceholder', language)}
                  className={`text-4xl font-bold bg-transparent w-full focus:outline-none focus:ring-1 focus:ring-pink-500 rounded-md p-1 -m-1 ${theme.text}`}
                />
            </div>
            <div className="flex items-center gap-2 flex-shrink-0 ml-4">
              <button onClick={handleOpenAssistant} className="p-2 rounded-full hover:bg-white/20 transition-colors" title={t('aiAssistantFabTooltip', language)}>
                <Icon icon="fa-robot" className="h-5 w-5"/>
              </button>
              <button onClick={handleDelete} className="p-2 rounded-full hover:bg-red-500/20 text-red-500 transition-colors">
                <Icon icon="fa-trash" className="h-5 w-5" />
              </button>
              <button onClick={handleExportAsMarkdown} className="p-2 rounded-full hover:bg-white/20 transition-colors" title={t('exportAsMarkdown', language)}>
                <Icon icon="fa-download" className="h-5 w-5"/>
              </button>
              <button onClick={handleBack} className={`px-4 py-2 rounded-full font-semibold shadow-lg transition-colors ${theme.accent} text-white`}>
                <Icon icon="fa-check" className="mr-2" /> {t('backAndSave', language)}
              </button>
            </div>
        </header>

      <div className="flex items-end">
        <TabButton tab="write" icon="fa-pencil" label={t('write', language)} />
        <TabButton tab="draw" icon="fa-palette" label={t('draw', language)} />
      </div>

      <div className={`${theme.cardBg} p-4 sm:p-6 rounded-b-lg rounded-tr-lg shadow-xl`}>
        {activeTab === 'write' ? (
          <div>
            <RichTextEditor value={content} onChange={setContent} theme={theme} onBlur={handleSave} />
            <div className="mt-4 flex gap-2">
              <button onClick={() => handleAiAction('summarize')} className={`px-3 py-1 text-sm rounded-full ${theme.secondaryBg} hover:opacity-80`}>{t('summarizeNote', language)}</button>
              <button onClick={() => handleAiAction('optimize')} className={`px-3 py-1 text-sm rounded-full ${theme.secondaryBg} hover:opacity-80`}>{t('optimizeText', language)}</button>
              <button onClick={() => handleAiAction('plan')} className={`px-3 py-1 text-sm rounded-full ${theme.secondaryBg} hover:opacity-80`}>{t('createItinerary', language)}</button>
            </div>
          </div>
        ) : (
          <DrawingCanvas dataUrl={drawingDataUrl} onSave={(data) => {setDrawingDataUrl(data); handleSave();}} theme={theme} language={language}/>
        )}
      </div>

      <div className="mt-8">
        <h3 className="text-2xl font-semibold mb-3">{t('subNotes', language)}</h3>
        <div className="space-y-2">
          {subNotes.map(subNote => (
            <div key={subNote.id} className={`flex justify-between items-center p-3 rounded-lg hover:bg-white/10 transition-colors ${theme.secondaryBg}`}>
                <button onClick={() => onNavigateToNote(subNote.id)} className="flex items-center gap-3 w-full">
                    <Icon icon="fa-file-alt" />
                    <span className="truncate">{subNote.title}</span>
                </button>
                 <button 
                    onClick={(e) => {
                        e.stopPropagation();
                        if (window.confirm(t('deleteConfirm', language))) {
                            onDelete(subNote.id);
                        }
                    }}
                    className="p-2 rounded-full hover:bg-red-500/20 text-red-500 transition-colors flex-shrink-0 ml-4"
                    title={t('delete', language)}
                >
                    <Icon icon="fa-trash" className="h-4 w-4" />
                </button>
            </div>
          ))}
        </div>
        <button onClick={handleCreateSubNote} className={`mt-3 w-full p-3 rounded-lg flex items-center justify-center gap-2 hover:opacity-90 transition-opacity ${theme.secondaryBg}`}>
            <Icon icon="fa-plus"/> {t('createSubNote', language)}
        </button>
      </div>

      <div className="mt-8">
        <h3 className="text-2xl font-semibold mb-3">{t('attachments', language)}</h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {attachments.map((att, index) => (
            <div key={index} className="relative group">
              <a href={att.dataUrl} download={att.name} className="block">
                {att.type.startsWith('image/') ? (
                  <img src={att.dataUrl} alt={att.name} className="w-full h-24 object-cover rounded-lg"/>
                ) : (
                  <div className="w-full h-24 rounded-lg flex flex-col items-center justify-center bg-gray-700 p-2">
                    <Icon icon="fa-file" className="text-3xl" />
                    <span className="text-xs text-center break-all mt-1">{att.name}</span>
                  </div>
                )}
              </a>
            </div>
          ))}
        </div>
        <button onClick={() => fileInputRef.current?.click()} className={`mt-3 w-full p-3 rounded-lg flex items-center justify-center gap-2 hover:opacity-90 transition-opacity ${theme.secondaryBg}`}>
          <Icon icon="fa-paperclip" /> {t('addAttachment', language)}
        </button>
        <input type="file" multiple ref={fileInputRef} onChange={handleAttachmentUpload} className="hidden" />
      </div>

       <Modal isOpen={isAiModalOpen} onClose={() => setAiModalOpen(false)} title={t('aiAssistant', language)} theme={theme}>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-48">
             <Icon icon="fa-spinner" className="fa-spin text-4xl mb-4"/>
             <p>{t('aiThinking', language)}</p>
          </div>
        ) : (
          <div>
            <div className={`p-4 rounded-md whitespace-pre-wrap ${theme.secondaryBg} max-h-96 overflow-y-auto`}>
              {aiResult}
            </div>
            <div className="flex justify-end gap-4 mt-4">
              <button onClick={applyAiResult} className={`px-4 py-2 rounded-md ${theme.accent} text-white hover:opacity-90`}>
                <Icon icon="fa-check" className="mr-2"/> {t('applyToNote', language)}
              </button>
            </div>
          </div>
        )}
      </Modal>

    </div>
  );
};

export default NoteEditor;
