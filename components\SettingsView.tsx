import React, { useState } from 'react';
import { Theme, Language } from '../types';
import Icon from './Icon';
import { t } from '../i18n';
import { themes } from '../constants';

interface SettingsViewProps {
  theme: Theme;
  language: Language;
  onThemeChange: (themeIndex: number) => void;
  onLanguageChange: () => void;
  currentThemeIndex: number;
}

const SettingsView: React.FC<SettingsViewProps> = ({ 
  theme, 
  language, 
  onThemeChange, 
  onLanguageChange, 
  currentThemeIndex 
}) => {
  const [exportFormat, setExportFormat] = useState<'json' | 'markdown'>('json');

  const handleExportData = () => {
    const data = localStorage.getItem('notes');
    if (!data) {
      alert(t('noDataToExport', language));
      return;
    }

    let content: string;
    let filename: string;
    let mimeType: string;

    if (exportFormat === 'json') {
      content = data;
      filename = `notes-backup-${new Date().toISOString().split('T')[0]}.json`;
      mimeType = 'application/json';
    } else {
      // Convert to markdown
      const notes = JSON.parse(data);
      content = notes.map((note: any) => {
        const textContent = new DOMParser().parseFromString(note.content, 'text/html').body.textContent || '';
        return `# ${note.title}\n\n${textContent}\n\n---\n`;
      }).join('\n');
      filename = `notes-export-${new Date().toISOString().split('T')[0]}.md`;
      mimeType = 'text/markdown';
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const data = JSON.parse(content);
        
        if (Array.isArray(data) && data.every(item => item.id && item.title)) {
          localStorage.setItem('notes', content);
          alert(t('importSuccess', language));
          window.location.reload(); // 重新加载页面以更新数据
        } else {
          alert(t('invalidFileFormat', language));
        }
      } catch (error) {
        alert(t('importError', language));
      }
    };
    reader.readAsText(file);
  };

  const handleClearData = () => {
    if (window.confirm(t('clearDataConfirm', language))) {
      localStorage.removeItem('notes');
      alert(t('dataClearedSuccess', language));
      window.location.reload();
    }
  };

  const getStorageInfo = () => {
    const data = localStorage.getItem('notes');
    if (!data) return { notesCount: 0, storageSize: '0 KB' };
    
    const notes = JSON.parse(data);
    const sizeInBytes = new Blob([data]).size;
    const sizeInKB = (sizeInBytes / 1024).toFixed(2);
    
    return {
      notesCount: notes.length,
      storageSize: `${sizeInKB} KB`
    };
  };

  const storageInfo = getStorageInfo();

  return (
    <div className="p-4 sm:p-8 max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold mb-8">{t('settings', language)}</h1>

      <div className="space-y-6">
        {/* 外观设置 */}
        <div className={`${theme.cardBg} p-6 rounded-xl shadow-lg`}>
          <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
            <Icon icon="fa-palette" />
            {t('appearance', language)}
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">{t('theme', language)}</label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {themes.map((themeOption, index) => (
                  <button
                    key={index}
                    onClick={() => onThemeChange(index)}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      currentThemeIndex === index
                        ? 'border-pink-500 ring-2 ring-pink-200'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className={`w-full h-8 rounded mb-2 ${themeOption.bg}`}></div>
                    <span className="text-sm font-medium">{themeOption.name[language]}</span>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">{t('language', language)}</label>
              <button
                onClick={onLanguageChange}
                className={`px-4 py-2 rounded-lg ${theme.secondaryBg} hover:opacity-80 transition-opacity`}
              >
                <Icon icon="fa-globe" className="mr-2" />
                {language === 'zh' ? '中文' : 'English'}
              </button>
            </div>
          </div>
        </div>

        {/* 数据管理 */}
        <div className={`${theme.cardBg} p-6 rounded-xl shadow-lg`}>
          <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
            <Icon icon="fa-database" />
            {t('dataManagement', language)}
          </h2>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div className={`${theme.secondaryBg} p-4 rounded-lg`}>
                <div className="text-sm opacity-60">{t('totalNotes', language)}</div>
                <div className="text-2xl font-bold">{storageInfo.notesCount}</div>
              </div>
              <div className={`${theme.secondaryBg} p-4 rounded-lg`}>
                <div className="text-sm opacity-60">{t('storageUsed', language)}</div>
                <div className="text-2xl font-bold">{storageInfo.storageSize}</div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">{t('exportFormat', language)}</label>
              <div className="flex gap-2 mb-3">
                <button
                  onClick={() => setExportFormat('json')}
                  className={`px-3 py-1 rounded text-sm ${
                    exportFormat === 'json' ? theme.accent + ' text-white' : theme.secondaryBg
                  }`}
                >
                  JSON
                </button>
                <button
                  onClick={() => setExportFormat('markdown')}
                  className={`px-3 py-1 rounded text-sm ${
                    exportFormat === 'markdown' ? theme.accent + ' text-white' : theme.secondaryBg
                  }`}
                >
                  Markdown
                </button>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleExportData}
                className={`px-4 py-2 rounded-lg ${theme.accent} text-white hover:opacity-90 transition-opacity flex items-center gap-2`}
              >
                <Icon icon="fa-download" />
                {t('exportData', language)}
              </button>
              
              <label className={`px-4 py-2 rounded-lg ${theme.secondaryBg} hover:opacity-80 transition-opacity cursor-pointer flex items-center gap-2`}>
                <Icon icon="fa-upload" />
                {t('importData', language)}
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportData}
                  className="hidden"
                />
              </label>
              
              <button
                onClick={handleClearData}
                className="px-4 py-2 rounded-lg bg-red-500 text-white hover:bg-red-600 transition-colors flex items-center gap-2"
              >
                <Icon icon="fa-trash" />
                {t('clearAllData', language)}
              </button>
            </div>
          </div>
        </div>

        {/* 关于 */}
        <div className={`${theme.cardBg} p-6 rounded-xl shadow-lg`}>
          <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
            <Icon icon="fa-info-circle" />
            {t('about', language)}
          </h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>{t('version', language)}</span>
              <span className="font-mono">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span>{t('buildDate', language)}</span>
              <span className="font-mono">{new Date().toLocaleDateString()}</span>
            </div>
            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm opacity-70">
                {t('appDescription', language)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsView;
