import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Note, View, Language } from './types';
import { useNotes } from './hooks/useNotes';
import NoteDashboard from './components/NoteDashboard';
import NoteEditor from './components/NoteEditor';
import AIAssistant from './components/AIAssistant';
import Layout from './components/Layout';
import { themes } from './constants';

const App: React.FC = () => {
  const { notes, addNote, updateNote, deleteNote } = useNotes();
  const [currentView, setCurrentView] = useState<View>('dashboard');
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [themeIndex, setThemeIndex] = useState(0);
  const [language, setLanguage] = useState<Language>('zh');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [isAssistantOpen, setIsAssistantOpen] = useState(false);
  const [assistantContext, setAssistantContext] = useState<{
    currentNote: Note;
    subNotes: Note[];
  } | null>(null);


  const currentTheme = themes[themeIndex];

  useEffect(() => {
    const root = document.documentElement;
    // Toggles the theme name class on the root <html> element for potential specific CSS rules.
    themes.forEach(theme => root.classList.remove(theme.className));
    root.classList.add(currentTheme.className);

    // Manage body classes for background and special effects
    const body = document.body;
    
    // A list of all possible classes that themes might add to the body.
    const allPossibleBgClasses = [...new Set(themes.flatMap(t => t.bg.split(' ')))];
    const allThemeBodyClasses = [
        'bg-gradient-to-r', 'from-indigo-500', 'via-purple-500', 'to-pink-500', 'animate-radiant-glow', 'bg-[length:200%_200%]',
        'animate-scanline', 'font-mono',
        ...allPossibleBgClasses
    ];

    // Clean up all theme-related classes and styles from body
    body.classList.remove(...allThemeBodyClasses);
    body.style.backgroundImage = '';
    body.style.backgroundSize = '';
    
    // Apply base background for the current theme
    body.classList.add(...currentTheme.bg.split(' '));
    
    // Apply special effects for specific themes
    if (currentTheme.className === 'radiant') {
        body.classList.remove(...currentTheme.bg.split(' ')); // remove bg-transparent
        body.classList.add('bg-gradient-to-r', 'from-indigo-500', 'via-purple-500', 'to-pink-500', 'animate-radiant-glow', 'bg-[length:200%_200%]');
    } else if (currentTheme.className === 'cyberpunk') {
        body.classList.add('animate-scanline');
        // A subtle scanline effect
        body.style.backgroundImage = 'linear-gradient(rgba(18, 18, 18, 0) 50%, rgba(0, 0, 0, 0.25) 50%)';
        body.style.backgroundSize = '100% 4px';
    } else if (currentTheme.className === 'terminal') {
        body.classList.add('font-mono');
    }
  }, [currentTheme]);


  const handleSelectNote = (id: string) => {
    setSelectedNoteId(id);
    setCurrentView('editor');
  };

  const handleCreateNewNote = () => {
    const newNote: Note = {
      id: `note_${Date.now()}`,
      title: 'Untitled Note',
      content: '<p>Start writing here...</p>',
      drawingDataUrl: null,
      attachments: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      motto: null,
      parentId: null,
    };
    addNote(newNote);
    setSelectedNoteId(newNote.id);
    setCurrentView('editor');
  };

  const handleBackToDashboard = () => {
    setSelectedNoteId(null);
    setCurrentView('dashboard');
  };

  const filteredNotes = useMemo(() => {
    const startTimestamp = startDate ? new Date(startDate).getTime() : 0;
    const endTimestamp = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : Infinity;

    return notes
      .filter(note => {
        // Only show top-level notes on the dashboard
        if (note.parentId !== null) {
          return false;
        }

        const termMatch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (note.content && note.content.toLowerCase().includes(searchTerm.toLowerCase()));
        
        const dateMatch = note.createdAt >= startTimestamp && note.createdAt <= endTimestamp;

        return termMatch && dateMatch;
      })
      .sort((a, b) => b.updatedAt - a.updatedAt);
  }, [notes, searchTerm, startDate, endDate]);

  const selectedNote = useMemo(() => {
    return notes.find(note => note.id === selectedNoteId) || null;
  }, [notes, selectedNoteId]);

  const toggleTheme = useCallback(() => {
    setThemeIndex(prevIndex => (prevIndex + 1) % themes.length);
  }, []);

  const toggleLanguage = useCallback(() => {
    setLanguage(prev => (prev === 'en' ? 'zh' : 'en'));
  }, []);

  const openAssistant = useCallback((context: { currentNote: Note; subNotes: Note[] } | null = null) => {
    setAssistantContext(context);
    setIsAssistantOpen(true);
  }, []);
  
  const closeAssistant = () => {
      setIsAssistantOpen(false);
      setAssistantContext(null);
  };

  return (
    <Layout
      currentTheme={currentTheme}
      toggleTheme={toggleTheme}
      language={language}
      toggleLanguage={toggleLanguage}
      currentView={currentView}
    >
      {currentView === 'dashboard' && (
        <NoteDashboard
          notes={filteredNotes}
          onSelectNote={handleSelectNote}
          onCreateNew={handleCreateNewNote}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          toggleTheme={toggleTheme}
          currentTheme={currentTheme}
          addNote={addNote}
          updateNote={updateNote}
          deleteNote={deleteNote}
          language={language}
          toggleLanguage={toggleLanguage}
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
          openAssistant={openAssistant}
        />
      )}
      {currentView === 'editor' && selectedNote && (
        <NoteEditor
          note={selectedNote}
          allNotes={notes}
          onSave={updateNote}
          onDelete={deleteNote}
          onBack={handleBackToDashboard}
          onNavigateToNote={handleSelectNote}
          addNote={addNote}
          theme={currentTheme}
          language={language}
          openAssistant={openAssistant}
        />
      )}
      <AIAssistant
        isOpen={isAssistantOpen}
        onClose={closeAssistant}
        theme={currentTheme}
        language={language}
        noteContext={assistantContext}
      />
    </Layout>
  );
};

export default App;