
import { Language } from './types';

const translations = {
  en: {
    memoTitle: 'Memo App',
    searchPlaceholder: 'Search notes...',
    emptyCanvasTitle: 'Your canvas is empty',
    emptyCanvasSubtitle: "Click the '+' button to create your first note.",
    saveAsNewNote: 'Save as New Note',
    downloadMD: 'Download .md',
    aiThinking: 'AI is thinking...',
    aiAssistant: 'AI Assistant',
    summarize: 'Summarize',
    plan: 'Plan',
    selectNotePrompt: 'Please select at least one note.',
    aiResultSaved: 'AI result saved as a new note!',
    backAndSave: 'Back & Save',
    deleteConfirm: 'Are you sure you want to delete this note and all its sub-notes?',
    noteTitlePlaceholder: 'Note Title',
    write: 'Write',
    draw: 'Draw',
    attachments: 'Attachments',
    addAttachment: 'Add Attachment',
    applyToNote: 'Apply to Note',
    summarizeNote: 'Summarize',
    optimizeText: 'Optimize Text',
    createItinerary: 'Create Itinerary',
    pen: 'Pen',
    eraser: 'Eraser',
    rectangle: 'Rectangle',
    circle: 'Circle',
    line: 'Line',
    pan: 'Pan/Move',
    lineWidth: 'Width',
    clear: 'Clear',
    delete: 'Delete',
    language: 'Language',
    toggleLanguage: '中文',
    startDate: 'Start Date',
    endDate: 'End Date',
    clearDateFilter: 'Clear Filter',
    aiAssistantFabTooltip: 'Open AI Assistant',
    aiAssistantTitle: 'AI Assistant',
    chatPlaceholder: 'Ask me anything or upload an image...',
    clearConversation: 'Clear Conversation',
    welcomeMessage: 'Hello! How can I help you today?',
    send: 'Send',
    uploadFile: 'Upload File',
    subNotes: 'Sub-notes',
    createSubNote: 'Create Sub-note',
    dashboard: 'Dashboard',
    subNotesContextTitle: 'Provide Sub-note Context',
    selectSubNotes: 'Select sub-notes to include',
  },
  zh: {
    memoTitle: '备忘录',
    searchPlaceholder: '搜索笔记...',
    emptyCanvasTitle: '你的画布是空的',
    emptyCanvasSubtitle: '点击“+”按钮创建你的第一条笔记。',
    saveAsNewNote: '另存为新笔记',
    downloadMD: '下载 .md',
    aiThinking: 'AI 正在思考...',
    aiAssistant: 'AI 助手',
    summarize: '总结',
    plan: '制定计划',
    selectNotePrompt: '请至少选择一篇笔记。',
    aiResultSaved: 'AI 已保存为新笔记！',
    backAndSave: '返回并保存',
    deleteConfirm: '确定要删除此笔记及其所有子笔记吗？',
    noteTitlePlaceholder: '笔记标题',
    write: '书写',
    draw: '绘画',
    attachments: '附件',
    addAttachment: '添加附件',
    applyToNote: '应用到笔记',
    summarizeNote: '总结笔记',
    optimizeText: '优化文本',
    createItinerary: '创建行程',
    pen: '画笔',
    eraser: '橡皮擦',
    rectangle: '矩形',
    circle: '圆形',
    line: '直线',
    pan: '平移/移动',
    lineWidth: '粗细',
    clear: '清空',
    delete: '删除',
    language: '语言',
    toggleLanguage: 'English',
    startDate: '开始日期',
    endDate: '结束日期',
    clearDateFilter: '清除筛选',
    aiAssistantFabTooltip: '打开AI助手',
    aiAssistantTitle: 'AI 助手',
    chatPlaceholder: '问我任何事或上传图片...',
    clearConversation: '清空对话',
    welcomeMessage: '你好！今天我能帮你什么？',
    send: '发送',
    uploadFile: '上传文件',
    subNotes: '子笔记',
    createSubNote: '创建子笔记',
    dashboard: '仪表盘',
    subNotesContextTitle: '提供子笔记上下文',
    selectSubNotes: '选择要包含的子笔记',
  }
};

export const t = (key: keyof typeof translations.en, lang: Language): string => {
  return translations[lang][key] || translations.en[key];
};
