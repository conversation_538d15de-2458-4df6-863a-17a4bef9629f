
import { Language } from './types';

const translations = {
  en: {
    memoTitle: 'Memo App',
    searchPlaceholder: 'Search notes...',
    emptyCanvasTitle: 'Your canvas is empty',
    emptyCanvasSubtitle: "Click the '+' button to create your first note.",
    saveAsNewNote: 'Save as New Note',
    downloadMD: 'Download .md',
    aiThinking: 'AI is thinking...',
    aiAssistant: 'AI Assistant',
    summarize: 'Summarize',
    plan: 'Plan',
    selectNotePrompt: 'Please select at least one note.',
    aiResultSaved: 'AI result saved as a new note!',
    backAndSave: 'Back & Save',
    deleteConfirm: 'Are you sure you want to delete this note and all its sub-notes?',
    noteTitlePlaceholder: 'Note Title',
    write: 'Write',
    draw: 'Draw',
    attachments: 'Attachments',
    addAttachment: 'Add Attachment',
    applyToNote: 'Apply to Note',
    summarizeNote: 'Summarize',
    optimizeText: 'Optimize Text',
    createItinerary: 'Create Itinerary',
    pen: 'Pen',
    eraser: 'Eraser',
    rectangle: 'Rectangle',
    circle: 'Circle',
    line: 'Line',
    pan: 'Pan/Move',
    lineWidth: 'Width',
    clear: 'Clear',
    delete: 'Delete',
    language: 'Language',
    toggleLanguage: '中文',
    startDate: 'Start Date',
    endDate: 'End Date',
    clearDateFilter: 'Clear Filter',
    aiAssistantFabTooltip: 'Open AI Assistant',
    aiAssistantTitle: 'AI Assistant',
    chatPlaceholder: 'Ask me anything or upload an image...',
    clearConversation: 'Clear Conversation',
    welcomeMessage: 'Hello! How can I help you today?',
    send: 'Send',
    uploadFile: 'Upload File',
    subNotes: 'Sub-notes',
    createSubNote: 'Create Sub-note',
    dashboard: 'Dashboard',
    subNotesContextTitle: 'Provide Sub-note Context',
    selectSubNotes: 'Select sub-notes to include',
    search: 'Search',
    settings: 'Settings',
    searchAll: 'All',
    searchTitle: 'Title',
    searchContent: 'Content',
    searchResults: 'Search results',
    notes: 'notes',
    noSearchResults: 'No search results',
    tryDifferentKeywords: 'Try different keywords',
    startSearching: 'Start searching',
    enterKeywordsToSearch: 'Enter keywords to search notes',
    hasDrawing: 'Has drawing',
    appearance: 'Appearance',
    theme: 'Theme',
    dataManagement: 'Data Management',
    totalNotes: 'Total Notes',
    storageUsed: 'Storage Used',
    exportFormat: 'Export Format',
    exportData: 'Export Data',
    importData: 'Import Data',
    clearAllData: 'Clear All Data',
    about: 'About',
    version: 'Version',
    buildDate: 'Build Date',
    appDescription: 'A powerful AI-powered memo application with rich text editing, drawing, and intelligent assistance.',
    noDataToExport: 'No data to export',
    importSuccess: 'Data imported successfully!',
    invalidFileFormat: 'Invalid file format',
    importError: 'Import failed',
    clearDataConfirm: 'Are you sure you want to clear all data? This action cannot be undone.',
    dataClearedSuccess: 'All data cleared successfully',
    exportAsMarkdown: 'Export as Markdown',
  },
  zh: {
    memoTitle: '备忘录',
    searchPlaceholder: '搜索笔记...',
    emptyCanvasTitle: '你的画布是空的',
    emptyCanvasSubtitle: '点击“+”按钮创建你的第一条笔记。',
    saveAsNewNote: '另存为新笔记',
    downloadMD: '下载 .md',
    aiThinking: 'AI 正在思考...',
    aiAssistant: 'AI 助手',
    summarize: '总结',
    plan: '制定计划',
    selectNotePrompt: '请至少选择一篇笔记。',
    aiResultSaved: 'AI 已保存为新笔记！',
    backAndSave: '返回并保存',
    deleteConfirm: '确定要删除此笔记及其所有子笔记吗？',
    noteTitlePlaceholder: '笔记标题',
    write: '书写',
    draw: '绘画',
    attachments: '附件',
    addAttachment: '添加附件',
    applyToNote: '应用到笔记',
    summarizeNote: '总结笔记',
    optimizeText: '优化文本',
    createItinerary: '创建行程',
    pen: '画笔',
    eraser: '橡皮擦',
    rectangle: '矩形',
    circle: '圆形',
    line: '直线',
    pan: '平移/移动',
    lineWidth: '粗细',
    clear: '清空',
    delete: '删除',
    language: '语言',
    toggleLanguage: 'English',
    startDate: '开始日期',
    endDate: '结束日期',
    clearDateFilter: '清除筛选',
    aiAssistantFabTooltip: '打开AI助手',
    aiAssistantTitle: 'AI 助手',
    chatPlaceholder: '问我任何事或上传图片...',
    clearConversation: '清空对话',
    welcomeMessage: '你好！今天我能帮你什么？',
    send: '发送',
    uploadFile: '上传文件',
    subNotes: '子笔记',
    createSubNote: '创建子笔记',
    dashboard: '仪表盘',
    subNotesContextTitle: '提供子笔记上下文',
    selectSubNotes: '选择要包含的子笔记',
    search: '搜索',
    settings: '设置',
    searchAll: '全部',
    searchTitle: '标题',
    searchContent: '内容',
    searchResults: '搜索结果',
    notes: '条笔记',
    noSearchResults: '没有搜索结果',
    tryDifferentKeywords: '尝试不同的关键词',
    startSearching: '开始搜索',
    enterKeywordsToSearch: '输入关键词搜索笔记',
    hasDrawing: '包含绘图',
    appearance: '外观',
    theme: '主题',
    dataManagement: '数据管理',
    totalNotes: '笔记总数',
    storageUsed: '存储使用',
    exportFormat: '导出格式',
    exportData: '导出数据',
    importData: '导入数据',
    clearAllData: '清空所有数据',
    about: '关于',
    version: '版本',
    buildDate: '构建日期',
    appDescription: '一个功能强大的AI驱动备忘录应用，支持富文本编辑、绘图和智能助手。',
    noDataToExport: '没有数据可导出',
    importSuccess: '数据导入成功！',
    invalidFileFormat: '无效的文件格式',
    importError: '导入失败',
    clearDataConfirm: '确定要清空所有数据吗？此操作无法撤销。',
    dataClearedSuccess: '所有数据已成功清空',
    exportAsMarkdown: '导出为Markdown',
  }
};

export const t = (key: keyof typeof translations.en, lang: Language): string => {
  return translations[lang][key] || translations.en[key];
};
