import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Note, Theme, Language } from '../types';
import { generateMotto } from '../services/geminiService';
import Icon from './Icon';
import { t } from '../i18n';
import { Check, Trash2, Calendar, Clock } from 'lucide-react';

interface NoteCardProps {
  note: Note;
  onSelect: (id: string) => void;
  theme: Theme;
  onUpdate: (note: Note) => void;
  isSelected: boolean;
  onToggleSelect: (id: string) => void;
  language: Language;
  onDelete: (id: string) => void;
  isSelectable?: boolean;
  openAssistant?: (context?: { currentNote: Note; subNotes: Note[] } | null) => void;
}

const NoteCard: React.FC<NoteCardProps> = ({ 
  note, 
  onSelect, 
  theme, 
  onUpdate, 
  isSelected, 
  onToggleSelect, 
  language, 
  onDelete,
  isSelectable = false,
  openAssistant
}) => {
  const [motto, setMotto] = useState(note.motto || '...');
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (!note.motto) {
      generateMotto(language).then(newMotto => {
        setMotto(newMotto);
        onUpdate({ ...note, motto: newMotto });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [note.motto, note.id, onUpdate, language]);

  // Create a temporary div to strip HTML for preview
  const plainTextContent = (htmlString: string) => {
    if (!htmlString) return "";
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlString;
    return tempDiv.textContent || tempDiv.innerText || "";
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if(window.confirm(t('deleteConfirm', language))) {
      onDelete(note.id);
    }
  }

  const handleSelect = (e: React.MouseEvent) => {
    if (isSelectable) {
      e.stopPropagation();
      onToggleSelect(note.id);
    } else {
      onSelect(note.id);
    }
  }

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return t('today', language);
    } else if (diffDays === 2) {
      return t('yesterday', language);
    } else if (diffDays <= 7) {
      return `${diffDays - 1} ${t('daysAgo', language)}`;
    } else {
      return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  }

  return (
    <motion.div
      className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-medium border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer transition-all duration-300 ${
        isSelected ? 'ring-2 ring-blue-500 shadow-medium' : ''
      }`}
      whileHover={{ y: -2 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={handleSelect}
      layout
    >
      {/* 选择指示器 */}
      {isSelectable && (
        <motion.div
          className="absolute top-3 right-3 z-10"
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: isHovered || isSelected ? 1 : 0,
            scale: isHovered || isSelected ? 1 : 0
          }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
              isSelected
                ? 'bg-blue-500 border-blue-500 text-white'
                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
            }`}
            whileTap={{ scale: 0.9 }}
          >
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.1 }}
              >
                <Check className="w-3 h-3" />
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}

      {/* 删除按钮 */}
      <motion.button
        className="absolute top-3 left-3 z-10 w-8 h-8 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors duration-200"
        initial={{ opacity: 0, scale: 0 }}
        animate={{
          opacity: isHovered ? 1 : 0,
          scale: isHovered ? 1 : 0
        }}
        transition={{ duration: 0.2 }}
        onClick={handleDelete}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <Trash2 className="w-4 h-4" />
      </motion.button>

      {/* 卡片内容 */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
            {note.title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed">
            {plainTextContent(note.content) || t('emptyNote', language)}
          </p>
        </div>

        {/* 附件指示器 */}
        {note.attachments && note.attachments.length > 0 && (
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-3">
            <Icon icon="fa-paperclip" className="mr-1" />
            <span>{note.attachments.length} {t('attachments', language)}</span>
          </div>
        )}

        {/* 时间信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              <span>{formatDate(note.createdAt)}</span>
            </div>
            {note.updatedAt !== note.createdAt && (
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                <span>{t('updated', language)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部座右铭 */}
      {motto && motto !== '...' && (
        <div className="px-6 pb-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 border-l-4 border-blue-500">
            <p className="text-xs italic text-gray-600 dark:text-gray-400 leading-relaxed">
              "{motto}"
            </p>
          </div>
        </div>
      )}

      {/* 加载动画覆盖层 */}
      {motto === '...' && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-800/50">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"
          />
        </div>
      )}
    </motion.div>
  );
};

export default NoteCard;
