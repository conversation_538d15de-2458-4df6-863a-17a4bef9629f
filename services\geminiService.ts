import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { Note, Language } from '../types';

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  // A simple alert for the user. In a real app, this would be a more robust notification.
  console.warn("API_KEY environment variable not set. AI features will be disabled.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY! });
const model = 'gemini-2.5-flash';

const callGemini = async (prompt: string): Promise<string> => {
    if(!API_KEY) return "AI is disabled. Please set your API_KEY.";
    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model,
            contents: prompt,
        });
        return response.text;
    } catch (error) {
        console.error("Gemini API call failed:", error);
        return "An error occurred while contacting the AI. Please check the console for details.";
    }
};

export const generateMotto = async (language: Language): Promise<string> => {
  const prompt = `Generate a one-sentence, inspirational, and thought-provoking quote in ${language === 'zh' ? 'Chinese' : 'English'}. It should be concise and memorable.`;
  return callGemini(prompt);
};

export const summarizeNote = async (content: string, language: Language): Promise<string> => {
  const prompt = `Please summarize the key points of the following note content. The summary should be concise and clear. Please respond in ${language === 'zh' ? 'Chinese' : 'English'}.\n\nNote Content:\n---\n${content}`;
  return callGemini(prompt);
};

export const optimizeText = async (content: string, language: Language): Promise<string> => {
  const prompt = `Please review and optimize the following text. Correct any grammatical errors, improve clarity and flow, and make it more polished, while preserving the original meaning. Please provide the optimized text in ${language === 'zh' ? 'Chinese' : 'English'}.\n\nOriginal Text:\n---\n${content}`;
  return callGemini(prompt);
};

export const createItinerary = async (content: string, language: Language): Promise<string> => {
  const prompt = `Based on the following notes, create a structured travel itinerary or a plan. Extract dates, locations, and activities, and present them in a logical, easy-to-read format. Please respond in ${language === 'zh' ? 'Chinese' : 'English'}.\n\nNote Content:\n---\n${content}`;
  return callGemini(prompt);
};

export const processMultipleNotes = async (notes: Note[], action: 'summarize' | 'plan', language: Language): Promise<string> => {
    const notesContent = notes.map((note, index) => 
        `Note ${index + 1}: ${note.title}\n${note.content}`
    ).join('\n\n---\n\n');

    const prompt = action === 'summarize'
        ? `I have several notes. Please provide a comprehensive summary that synthesizes the key themes and information from all of them. Respond in ${language === 'zh' ? 'Chinese' : 'English'}.\n\nNotes:\n---\n${notesContent}`
        : `Based on the collection of notes provided, please create a detailed action plan or a coherent travel itinerary. Combine the information logically. Respond in ${language === 'zh' ? 'Chinese' : 'English'}.\n\nNotes:\n---\n${notesContent}`;

    return callGemini(prompt);
};
