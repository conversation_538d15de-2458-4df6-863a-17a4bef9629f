
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Note, Theme, Language } from '../types';
import NoteCard from './NoteCard';
import Icon from './Icon';
import Modal from './Modal';
import { processMultipleNotes } from '../services/geminiService';
import { t } from '../i18n';
import { Plus, Search, Calendar, Filter, Download, Bot, Trash2 } from 'lucide-react';

interface NoteDashboardProps {
  notes: Note[];
  onSelectNote: (id: string) => void;
  onCreateNew: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  toggleTheme: () => void;
  currentTheme: Theme;
  addNote: (note: Note) => void;
  updateNote: (note: Note) => void;
  deleteNote: (id: string) => void;
  language: Language;
  toggleLanguage: () => void;
  startDate: string;
  setStartDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
  openAssistant: (context?: { currentNote: Note; subNotes: Note[] } | null) => void;
}

const NoteDashboard: React.FC<NoteDashboardProps> = ({ notes, onSelectNote, onCreateNew, searchTerm, setSearchTerm, toggleTheme, currentTheme, addNote, updateNote, deleteNote, language, toggleLanguage, startDate, setStartDate, endDate, setEndDate, openAssistant }) => {
  const [selectedNoteIds, setSelectedNoteIds] = useState<Set<string>>(new Set());
  const [isAiModalOpen, setIsAiModalOpen] = useState(false);
  const [aiResult, setAiResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleToggleSelect = (id: string) => {
    setSelectedNoteIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleAiAction = async (action: 'summarize' | 'plan') => {
    if(selectedNoteIds.size === 0) {
        alert(t('selectNotePrompt', language));
        return;
    }
    setIsLoading(true);
    setAiResult('');
    setIsAiModalOpen(true);
    const selectedNotes = notes.filter(note => selectedNoteIds.has(note.id));
    const result = await processMultipleNotes(selectedNotes, action, language);
    setAiResult(result);
    setIsLoading(false);
  };
  
  const saveAiResultAsNote = () => {
    if(!aiResult) return;
    const newNote: Note = {
      id: `note_${Date.now()}`,
      title: 'AI Generated Summary/Plan',
      content: `<p>${aiResult.replace(/\n/g, '</p><p>')}</p>`,
      drawingDataUrl: null,
      attachments: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      motto: null,
      parentId: null,
    };
    addNote(newNote);
    setIsAiModalOpen(false);
    setSelectedNoteIds(new Set());
    alert(t('aiResultSaved', language));
  };

  const downloadAsMarkdown = () => {
    if(!aiResult) return;
    const blob = new Blob([aiResult], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ai-generated-plan.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClearDateFilter = () => {
    setStartDate('');
    setEndDate('');
  };
  
  return (
    <div className="p-6">
      {/* 页面标题和操作栏 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t('dashboard', language)}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('dashboardSubtitle', language)} ({notes.length} {t('notes', language)})
            </p>
          </div>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onCreateNew}
            className="mt-4 lg:mt-0 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-xl font-medium flex items-center space-x-2 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Plus className="w-5 h-5" />
            <span>{t('createNewNote', language)}</span>
          </motion.button>
        </div>

        {/* 搜索和筛选栏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder={t('searchPlaceholder', language)}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleClearDateFilter}
            className="px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <Filter className="w-5 h-5" />
            <span>{t('clearFilter', language)}</span>
          </motion.button>
        </div>

        {/* 批量操作栏 */}
        <AnimatePresence>
          {selectedNoteIds.size > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6"
            >
              <div className="flex flex-col sm:flex-row items-center justify-between">
                <p className="text-blue-700 dark:text-blue-300 mb-3 sm:mb-0">
                  {selectedNoteIds.size} {t('notesSelected', language)}
                </p>
                <div className="flex space-x-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAiAction('summarize')}
                    className="bg-green-500 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2 hover:bg-green-600 transition-colors duration-200"
                  >
                    <Bot className="w-4 h-4" />
                    <span>{t('summarize', language)}</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAiAction('plan')}
                    className="bg-purple-500 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2 hover:bg-purple-600 transition-colors duration-200"
                  >
                    <Bot className="w-4 h-4" />
                    <span>{t('makePlan', language)}</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedNoteIds(new Set())}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors duration-200"
                  >
                    {t('cancel', language)}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* 笔记网格 */}
      {notes.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center py-20"
        >
          <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
            <Plus className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('noNotesYet', language)}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('createFirstNote', language)}
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onCreateNew}
            className="bg-blue-500 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-600 transition-colors duration-200"
          >
            {t('createNewNote', language)}
          </motion.button>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <AnimatePresence>
            {notes.map((note, index) => (
              <motion.div
                key={note.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ 
                  duration: 0.3, 
                  delay: index * 0.1,
                  ease: 'easeOut'
                }}
                whileHover={{ y: -5 }}
                className="cursor-pointer"
              >
                <NoteCard
                  note={note}
                  onSelect={onSelectNote}
                  onUpdate={updateNote}
                  onDelete={deleteNote}
                  theme={currentTheme}
                  language={language}
                  isSelectable={true}
                  isSelected={selectedNoteIds.has(note.id)}
                  onToggleSelect={handleToggleSelect}
                  openAssistant={openAssistant}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* AI处理结果模态框 */}
      <Modal 
        isOpen={isAiModalOpen} 
        onClose={() => setIsAiModalOpen(false)} 
        title={t('aiAssistant', language)} 
        theme={currentTheme}
      >
        {isLoading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center h-48"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-4"
            />
            <p className="text-gray-600 dark:text-gray-400">{t('aiThinking', language)}</p>
          </motion.div>
        ) : (
          <div>
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl max-h-96 overflow-y-auto mb-4 whitespace-pre-wrap">
              {aiResult}
            </div>
            <div className="flex justify-end gap-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={downloadAsMarkdown}
                className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium flex items-center space-x-2 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                <Download className="w-4 h-4" />
                <span>{t('downloadMD', language)}</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={saveAiResultAsNote}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors duration-200"
              >
                {t('saveAsNewNote', language)}
              </motion.button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default NoteDashboard;