export type Language = 'en' | 'zh';

export interface Note {
  id: string;
  title: string;
  content: string; // HTML content from rich text editor
  drawingDataUrl: string | null;
  attachments: Attachment[];
  createdAt: number;
  updatedAt: number;
  motto: string | null;
  parentId: string | null;
}

export interface Attachment {
  name: string;
  type: string;
  size: number;
  dataUrl: string;
}

export type View = 'dashboard' | 'editor';

export interface Theme {
  name: { [key in Language]: string };
  bg: string;
  text: string;
  cardBg: string;
  cardText: string;
  accent: string;
  secondaryBg: string;
  className: string;
}

export interface Part {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string;
  };
}

export interface ChatMessage {
  role: 'user' | 'model';
  parts: Part[];
}
