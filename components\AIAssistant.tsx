
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GoogleGenAI, Chat } from "@google/genai";
import { Theme, Language, ChatMessage, Part, Note } from '../types';
import Modal from './Modal';
import Icon from './Icon';
import { t } from '../i18n';

interface AIAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  theme: Theme;
  language: Language;
  noteContext?: {
    currentNote: Note;
    subNotes: Note[];
  } | null;
}

const API_KEY = process.env.API_KEY;

const fileToGenerativePart = async (file: File) => {
    const base64EncodedDataPromise = new Promise<string>((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
      reader.readAsDataURL(file);
    });
    return {
      inlineData: {
        data: await base64EncodedDataPromise,
        mimeType: file.type,
      },
    };
  };

const AIAssistant: React.FC<AIAssistantProps> = ({ isOpen, onClose, theme, language, noteContext }) => {
  const [chat, setChat] = useState<Chat | null>(null);
  const [history, setHistory] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedSubNoteIds, setSelectedSubNoteIds] = useState<Set<string>>(new Set());
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const initChat = useCallback(() => {
    if (!API_KEY) {
      console.warn("API key not found for AI Assistant.");
      setHistory([{ role: 'model', parts: [{text: 'AI features are disabled. API_KEY is not configured.'}] }]);
      return;
    }
    const ai = new GoogleGenAI({ apiKey: API_KEY });
    const newChat = ai.chats.create({
      model: 'gemini-2.5-flash',
      config: {
        systemInstruction: 'You are a friendly and helpful AI assistant in a note-taking application. Your name is Memo. Be concise unless asked for details.',
      }
    });
    setChat(newChat);
    setHistory([{ role: 'model', parts: [{ text: t('welcomeMessage', language) }] }]);
    setUserInput('');
    setImageFile(null);
    setImagePreview(null);
    setSelectedSubNoteIds(new Set());
  }, [language]);

  useEffect(() => {
    if (isOpen) {
        if (!chat) {
          initChat();
        }
        // Reset selections when modal is opened/re-opened
        setSelectedSubNoteIds(new Set());
    }
  }, [isOpen, chat, initChat]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [history]);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            alert("File size exceeds 5MB limit.");
            return;
        }
        setImageFile(file);
        setImagePreview(URL.createObjectURL(file));
    }
    // reset file input value to allow selecting the same file again
    e.target.value = ''; 
  }

  const handleToggleSubNote = (id: string) => {
    setSelectedSubNoteIds(prev => {
        const newSet = new Set(prev);
        if (newSet.has(id)) {
            newSet.delete(id);
        } else {
            newSet.add(id);
        }
        return newSet;
    });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if ((!userInput.trim() && !imageFile) || isLoading || !chat) return;
    
    setIsLoading(true);

    // This part is for the visual chat history
    const userMessageForHistory: Part[] = [];
    if (imageFile) {
        // We'll use the preview URL for display, not the base64 data
        userMessageForHistory.push({ inlineData: { mimeType: imageFile.type, data: imagePreview! } });
    }
    if (userInput.trim()) {
        userMessageForHistory.push({ text: userInput.trim() });
    }

    // This part is for the API call, potentially with context
    let contextPreamble = '';
    if (noteContext && selectedSubNoteIds.size > 0) {
        const selectedNotes = noteContext.subNotes.filter(n => selectedSubNoteIds.has(n.id));
        if (selectedNotes.length > 0) {
            const subNotesContent = selectedNotes
                .map(n => `--- Sub-note: ${n.title} ---\n${new DOMParser().parseFromString(n.content, 'text/html').body.textContent || '[empty]'}`)
                .join('\n');
            
            contextPreamble = `Given the following context from my notes:\n\n${subNotesContent}\n\nNow, please respond to the following:\n`;
        }
    }
    const fullUserInputForApi = contextPreamble + userInput.trim();

    const apiMessageParts: Part[] = [];
     if (imageFile) {
        const imagePart = await fileToGenerativePart(imageFile);
        apiMessageParts.push(imagePart);
    }
    if (fullUserInputForApi) {
        apiMessageParts.push({ text: fullUserInputForApi });
    }

    const updatedHistory: ChatMessage[] = [...history, { role: 'user', parts: userMessageForHistory }];
    setHistory(updatedHistory);
    
    setUserInput('');
    setImageFile(null);
    setImagePreview(null);
    setSelectedSubNoteIds(new Set()); // Clear selection after sending

    try {
      const response = await chat.sendMessageStream({ message: apiMessageParts });
      
      let currentModelResponse = '';
      setHistory(prev => [...prev, { role: 'model', parts: [{text: ''}] }]);

      for await (const chunk of response) {
        currentModelResponse += chunk.text;
        setHistory(prev => {
          const newHistory = [...prev];
          newHistory[newHistory.length - 1] = { role: 'model', parts: [{ text: currentModelResponse }] };
          return newHistory;
        });
      }
    } catch (error) {
      console.error("AI Assistant send message failed:", error);
      setHistory(prev => [...prev, { role: 'model', parts: [{ text: "Sorry, I encountered an error. Please try again."}] }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    initChat();
  };
  
  const modalTitle = (
      <div className="flex justify-between items-center w-full">
          <span>{t('aiAssistantTitle', language)}</span>
          <button onClick={handleClear} className="text-sm px-3 py-1 rounded-full hover:bg-white/20 transition-colors flex items-center gap-1.5 ml-4" title={t('clearConversation', language)}>
              <Icon icon="fa-broom" className="h-3 w-3" />
              <span>{t('clearConversation', language)}</span>
          </button>
      </div>
  )

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={modalTitle} theme={theme}>
      <div className="flex flex-col h-[60vh]">
         {noteContext && noteContext.subNotes.length > 0 && (
            <div className={`mb-3 pb-3 border-b ${theme.secondaryBg} border-opacity-50`}>
                <h4 className="font-semibold mb-2 text-sm opacity-90">{t('subNotesContextTitle', language)}</h4>
                <div className="max-h-24 overflow-y-auto space-y-1 pr-2">
                    {noteContext.subNotes.map(subNote => (
                        <div key={subNote.id} className={`flex items-center p-1.5 rounded-md`}>
                            <input
                                type="checkbox"
                                id={`subnote-checkbox-${subNote.id}`}
                                checked={selectedSubNoteIds.has(subNote.id)}
                                onChange={() => handleToggleSubNote(subNote.id)}
                                className="h-4 w-4 mr-3 rounded text-pink-500 focus:ring-pink-400 bg-gray-700 border-gray-600 cursor-pointer"
                            />
                            <label htmlFor={`subnote-checkbox-${subNote.id}`} className="text-sm truncate cursor-pointer select-none">{subNote.title}</label>
                        </div>
                    ))}
                </div>
            </div>
        )}
        <div ref={chatContainerRef} className="flex-grow overflow-y-auto pr-2 space-y-4">
          {history.map((msg, index) => (
            <div key={index} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div 
                className={`max-w-xs md:max-w-md lg:max-w-lg px-4 py-3 rounded-2xl whitespace-pre-wrap break-words flex flex-col gap-2 ${
                  msg.role === 'user'
                    ? `${theme.accent} text-white rounded-br-none`
                    : `${theme.secondaryBg} ${theme.text} rounded-bl-none`
                }`}
              >
                {msg.parts.map((part, partIndex) => {
                    if (part.inlineData) {
                        // For user history, use the object URL preview. For model history, use base64.
                        const src = msg.role === 'user' ? part.inlineData.data : `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`;
                        return <img key={partIndex} src={src} alt="user upload" className="rounded-lg max-h-64 w-auto"/>
                    }
                    return <span key={partIndex}>{part.text}</span>
                })}
                {isLoading && msg.role === 'model' && index === history.length - 1 && <Icon icon="fa-spinner" className="fa-spin ml-2 inline-block"/>}
              </div>
            </div>
          ))}
        </div>
        
        {imagePreview && (
            <div className='mt-2 p-2 border-t flex items-start gap-2'>
                <img src={imagePreview} alt="preview" className="h-16 w-16 object-cover rounded-md"/>
                <button onClick={() => { setImageFile(null); setImagePreview(null); }} className='p-1 rounded-full hover:bg-black/20 text-white bg-black/50'>
                    <Icon icon="fa-times" className="h-3 w-3"/>
                </button>
            </div>
        )}

        <form onSubmit={handleSendMessage} className="mt-4 flex items-center gap-2">
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className={`p-3 rounded-lg ${theme.secondaryBg} hover:opacity-80 transition-opacity`}
              title={t('uploadFile', language)}
            >
              <Icon icon="fa-paperclip" className="h-5 w-5"/>
            </button>
            <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden"/>

          <input
            type="text"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder={t('chatPlaceholder', language)}
            disabled={isLoading || !API_KEY}
            className={`w-full p-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 ${theme.secondaryBg} ${theme.text}`}
          />
          <button
            type="submit"
            disabled={isLoading || (!userInput.trim() && !imageFile)}
            className={`p-3 rounded-lg ${theme.accent} text-white flex-shrink-0 disabled:opacity-50 disabled:cursor-not-allowed hover:opacity-90 transition-opacity`}
            aria-label={t('send', language)}
          >
            <Icon icon="fa-paper-plane" className="h-5 w-5" />
          </button>
        </form>
      </div>
    </Modal>
  );
};

export default AIAssistant;