
import React from 'react';
import { Theme } from '../types';
import Icon from './Icon';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: React.ReactNode;
  children: React.ReactNode;
  theme: Theme;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, theme }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex justify-center items-center z-50 p-4">
      <div 
        className={`rounded-lg shadow-2xl w-full max-w-2xl transform transition-all duration-300 ${theme.cardBg} border ${theme.secondaryBg}`}
        onClick={e => e.stopPropagation()}
      >
        <div className={`flex justify-between items-center p-4 border-b ${theme.secondaryBg} rounded-t-lg`}>
          <div className={`text-xl font-bold ${theme.text}`}>{title}</div>
          <button onClick={onClose} className={`p-2 rounded-full hover:bg-white/20 transition-colors ${theme.text}`}>
            <Icon icon="fa-times" />
          </button>
        </div>
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
