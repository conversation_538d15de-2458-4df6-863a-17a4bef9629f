import { Theme } from './types';

export const themes: Theme[] = [
  {
    name: { en: 'Default', zh: '默认' },
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    cardBg: 'bg-white',
    cardText: 'text-gray-700',
    accent: 'bg-blue-500',
    secondaryBg: 'bg-gray-200',
    className: 'light',
  },
  {
    name: { en: 'Dark Mode', zh: '暗黑模式' },
    bg: 'bg-gray-900',
    text: 'text-gray-100',
    cardBg: 'bg-gray-800',
    cardText: 'text-gray-300',
    accent: 'bg-teal-500',
    secondaryBg: 'bg-gray-700',
    className: 'dark',
  },
  {
    name: { en: 'Radiant Streamer', zh: '流光炫彩' },
    bg: 'bg-transparent',
    text: 'text-white',
    cardBg: 'bg-black/30 backdrop-blur-md',
    cardText: 'text-gray-100',
    accent: 'bg-pink-500',
    secondaryBg: 'bg-white/20',
    className: 'radiant',
  },
  {
    name: { en: 'Cyberpunk', zh: '赛博朋克' },
    bg: 'bg-slate-900',
    text: 'text-cyan-400',
    cardBg: 'bg-slate-800/50 backdrop-blur-sm border border-cyan-500/20',
    cardText: 'text-cyan-300',
    accent: 'bg-fuchsia-600',
    secondaryBg: 'bg-slate-700/50',
    className: 'cyberpunk',
  },
  {
    name: { en: 'Terminal', zh: '终端' },
    bg: 'bg-black',
    text: 'text-green-400 font-mono',
    cardBg: 'bg-black border border-green-700/50',
    cardText: 'text-green-400',
    accent: 'bg-green-500',
    secondaryBg: 'bg-green-900/50',
    className: 'terminal',
  },
  {
    name: { en: 'Evergreen', zh: '常青' },
    bg: 'bg-green-50',
    text: 'text-gray-800',
    cardBg: 'bg-white',
    cardText: 'text-gray-700',
    accent: 'bg-emerald-600',
    secondaryBg: 'bg-green-100',
    className: 'evergreen',
  },
  {
    name: { en: 'Sakura', zh: '樱花' },
    bg: 'bg-pink-50',
    text: 'text-gray-800',
    cardBg: 'bg-white',
    cardText: 'text-gray-700',
    accent: 'bg-rose-400',
    secondaryBg: 'bg-pink-100',
    className: 'sakura',
  },
];
