
import { useState, useEffect, useCallback } from 'react';
import { Note } from '../types';

export const useNotes = () => {
  const [notes, setNotes] = useState<Note[]>(() => {
    try {
      const savedNotes = localStorage.getItem('notes-app-data');
      return savedNotes ? JSON.parse(savedNotes) : [];
    } catch (error) {
      console.error('Failed to load notes from localStorage', error);
      return [];
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem('notes-app-data', JSON.stringify(notes));
    } catch (error) {
      console.error('Failed to save notes to localStorage', error);
    }
  }, [notes]);

  const addNote = useCallback((newNote: Note) => {
    setNotes(prevNotes => [...prevNotes, newNote]);
  }, []);

  const updateNote = useCallback((updatedNote: Note) => {
    setNotes(prevNotes =>
      prevNotes.map(note =>
        note.id === updatedNote.id ? { ...updatedNote, updatedAt: Date.now() } : note
      )
    );
  }, []);

  const deleteNote = useCallback((id: string) => {
    setNotes(prevNotes => {
      const notesToDelete = new Set<string>();
      const queue: string[] = [id];
      notesToDelete.add(id);

      while (queue.length > 0) {
        const currentId = queue.shift()!;
        const children = prevNotes.filter(note => note.parentId === currentId);
        for (const child of children) {
          if (!notesToDelete.has(child.id)) {
            notesToDelete.add(child.id);
            queue.push(child.id);
          }
        }
      }
      
      return prevNotes.filter(note => !notesToDelete.has(note.id));
    });
  }, []);

  return { notes, addNote, updateNote, deleteNote };
};
