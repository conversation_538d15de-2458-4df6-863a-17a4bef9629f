import React, { useState, useMemo } from 'react';
import { Note, Theme, Language } from '../types';
import Icon from './Icon';
import { t } from '../i18n';

interface SearchViewProps {
  notes: Note[];
  onSelectNote: (id: string) => void;
  theme: Theme;
  language: Language;
}

const SearchView: React.FC<SearchViewProps> = ({ notes, onSelectNote, theme, language }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState<'all' | 'title' | 'content'>('all');

  const searchResults = useMemo(() => {
    if (!searchTerm.trim()) return [];

    const term = searchTerm.toLowerCase();
    
    return notes.filter(note => {
      switch (searchType) {
        case 'title':
          return note.title.toLowerCase().includes(term);
        case 'content':
          const textContent = new DOMParser().parseFromString(note.content, 'text/html').body.textContent || '';
          return textContent.toLowerCase().includes(term);
        case 'all':
        default:
          const allTextContent = new DOMParser().parseFromString(note.content, 'text/html').body.textContent || '';
          return note.title.toLowerCase().includes(term) || allTextContent.toLowerCase().includes(term);
      }
    }).sort((a, b) => b.updatedAt - a.updatedAt);
  }, [notes, searchTerm, searchType]);

  const highlightText = (text: string, term: string) => {
    if (!term) return text;
    const regex = new RegExp(`(${term})`, 'gi');
    const parts = text.split(regex);
    return parts.map((part, index) => 
      regex.test(part) ? 
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">{part}</mark> : 
        part
    );
  };

  const getPreview = (content: string, maxLength: number = 150) => {
    const textContent = new DOMParser().parseFromString(content, 'text/html').body.textContent || '';
    return textContent.length > maxLength ? textContent.substring(0, maxLength) + '...' : textContent;
  };

  return (
    <div className="p-4 sm:p-8 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-6">{t('search', language)}</h1>
        
        {/* 搜索输入框 */}
        <div className={`${theme.cardBg} p-6 rounded-xl shadow-lg mb-6`}>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Icon icon="fa-search" className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={t('searchPlaceholder', language)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-pink-500 ${theme.cardBg} ${theme.text}`}
                />
              </div>
            </div>
            
            {/* 搜索类型选择 */}
            <div className="flex gap-2">
              {[
                { key: 'all', label: t('searchAll', language) },
                { key: 'title', label: t('searchTitle', language) },
                { key: 'content', label: t('searchContent', language) }
              ].map(({ key, label }) => (
                <button
                  key={key}
                  onClick={() => setSearchType(key as typeof searchType)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    searchType === key
                      ? `${theme.accent} text-white`
                      : `${theme.secondaryBg} hover:opacity-80`
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 搜索统计 */}
        {searchTerm && (
          <div className="mb-4 text-sm opacity-70">
            {t('searchResults', language)}: {searchResults.length} {t('notes', language)}
          </div>
        )}
      </div>

      {/* 搜索结果 */}
      <div className="space-y-4">
        {searchTerm ? (
          searchResults.length > 0 ? (
            searchResults.map(note => (
              <div
                key={note.id}
                onClick={() => onSelectNote(note.id)}
                className={`${theme.cardBg} p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:scale-[1.02]`}
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-semibold">
                    {highlightText(note.title, searchTerm)}
                  </h3>
                  <div className="text-sm opacity-60">
                    {new Date(note.updatedAt).toLocaleDateString()}
                  </div>
                </div>
                
                <p className="opacity-80 mb-3">
                  {highlightText(getPreview(note.content), searchTerm)}
                </p>
                
                <div className="flex items-center gap-4 text-sm opacity-60">
                  <span className="flex items-center gap-1">
                    <Icon icon="fa-calendar" />
                    {new Date(note.createdAt).toLocaleDateString()}
                  </span>
                  {note.attachments.length > 0 && (
                    <span className="flex items-center gap-1">
                      <Icon icon="fa-paperclip" />
                      {note.attachments.length}
                    </span>
                  )}
                  {note.drawingDataUrl && (
                    <span className="flex items-center gap-1">
                      <Icon icon="fa-palette" />
                      {t('hasDrawing', language)}
                    </span>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className={`${theme.cardBg} p-12 rounded-xl shadow-lg text-center`}>
              <Icon icon="fa-search" className="text-6xl opacity-30 mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t('noSearchResults', language)}</h3>
              <p className="opacity-60">{t('tryDifferentKeywords', language)}</p>
            </div>
          )
        ) : (
          <div className={`${theme.cardBg} p-12 rounded-xl shadow-lg text-center`}>
            <Icon icon="fa-search" className="text-6xl opacity-30 mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('startSearching', language)}</h3>
            <p className="opacity-60">{t('enterKeywordsToSearch', language)}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchView;
