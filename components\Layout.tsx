import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Menu, 
  X, 
  Home, 
  Edit3, 
  Settings, 
  Moon, 
  Sun, 
  Globe,
  Bot,
  FileText,
  Search,
  Calendar,
  Info
} from 'lucide-react';
import { Theme, Language } from '../types';

interface LayoutProps {
  children: React.ReactNode;
  currentTheme: Theme;
  toggleTheme: () => void;
  language: Language;
  toggleLanguage: () => void;
  currentView: 'dashboard' | 'editor' | 'search' | 'assistant' | 'settings';
  onViewChange: (view: 'dashboard' | 'search' | 'assistant' | 'settings') => void;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  currentTheme,
  toggleTheme,
  language,
  toggleLanguage,
  currentView,
  onViewChange
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    setIsDarkMode(currentTheme.className === 'dark');
  }, [currentTheme]);

  const sidebarItems = [
    { icon: Home, label: '仪表盘', id: 'dashboard', active: currentView === 'dashboard' },
    { icon: Edit3, label: '编辑器', id: 'editor', active: currentView === 'editor' },
    { icon: Search, label: '搜索', id: 'search', active: currentView === 'search' },
    { icon: Bot, label: 'AI助手', id: 'assistant', active: currentView === 'assistant' },
    { icon: Settings, label: '设置', id: 'settings', active: currentView === 'settings' },
  ];

  const sidebarVariants = {
    expanded: { width: 256, transition: { duration: 0.3, ease: 'easeInOut' } },
    collapsed: { width: 64, transition: { duration: 0.3, ease: 'easeInOut' } }
  };

  const contentVariants = {
    expanded: { marginLeft: 256, transition: { duration: 0.3, ease: 'easeInOut' } },
    collapsed: { marginLeft: 64, transition: { duration: 0.3, ease: 'easeInOut' } }
  };

  const sidebarItemVariants = {
    expanded: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.2, delay: 0.1 }
    },
    collapsed: { 
      opacity: 0, 
      x: -20,
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className={`min-h-screen ${currentTheme.bg} ${currentTheme.text} transition-colors duration-300`}>
      {/* 侧边栏 */}
      <motion.aside
        className={`fixed left-0 top-0 h-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-r border-gray-200/50 dark:border-gray-700/50 z-40 flex flex-col`}
        variants={sidebarVariants}
        animate={sidebarCollapsed ? 'collapsed' : 'expanded'}
        initial="expanded"
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200/50 dark:border-gray-700/50">
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                variants={sidebarItemVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                className="flex items-center space-x-2"
              >
                <FileText className="w-6 h-6 text-blue-600" />
                <span className="font-semibold text-gray-900 dark:text-white">AI备忘录</span>
              </motion.div>
            )}
          </AnimatePresence>
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            {sidebarCollapsed ? (
              <Menu className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            ) : (
              <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            )}
          </button>
        </div>

        {/* 导航项 */}
        <nav className="flex-1 p-2 space-y-1">
          {sidebarItems.map((item) => (
            <div key={item.id} className="relative group">
              <button
                onClick={() => {
                  if (item.id === 'dashboard' || item.id === 'search' || item.id === 'assistant' || item.id === 'settings') {
                    onViewChange(item.id);
                  }
                }}
                className={`w-full flex items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                  item.active
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                <item.icon className="w-5 h-5 flex-shrink-0" />
                <AnimatePresence>
                  {!sidebarCollapsed && (
                    <motion.span
                      variants={sidebarItemVariants}
                      initial="collapsed"
                      animate="expanded"
                      exit="collapsed"
                      className="ml-3 text-sm font-medium truncate"
                    >
                      {item.label}
                    </motion.span>
                  )}
                </AnimatePresence>
              </button>
              
              {/* 工具提示 */}
              {sidebarCollapsed && (
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.label}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* 侧边栏底部 */}
        <div className="p-2 border-t border-gray-200/50 dark:border-gray-700/50">
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                variants={sidebarItemVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400"
              >
                <div className="flex items-center justify-between mb-1">
                  <span>版本 1.0.0</span>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
                <div>系统运行正常</div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.aside>

      {/* 顶部导航栏 */}
      <motion.header
        className="fixed top-0 right-0 h-16 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 z-30 flex items-center justify-end px-6"
        variants={contentVariants}
        animate={sidebarCollapsed ? 'collapsed' : 'expanded'}
        style={{ left: sidebarCollapsed ? 64 : 256 }}
      >
        <div className="flex items-center space-x-4">
          {/* 主题切换 */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 hover:scale-105"
          >
            {isDarkMode ? (
              <Sun className="w-5 h-5 text-yellow-500" />
            ) : (
              <Moon className="w-5 h-5 text-gray-600" />
            )}
          </button>

          {/* 语言切换 */}
          <button
            onClick={toggleLanguage}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 hover:scale-105"
          >
            <Globe className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <span className="ml-1 text-sm font-medium text-gray-700 dark:text-gray-300">
              {language.toUpperCase()}
            </span>
          </button>

          {/* 用户头像 */}
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">U</span>
          </div>
        </div>
      </motion.header>

      {/* 主内容区域 */}
      <motion.main
        className="pt-16"
        variants={contentVariants}
        animate={sidebarCollapsed ? 'collapsed' : 'expanded'}
        style={{ marginLeft: sidebarCollapsed ? 64 : 256 }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentView}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="min-h-[calc(100vh-4rem)]"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </motion.main>
    </div>
  );
};

export default Layout;